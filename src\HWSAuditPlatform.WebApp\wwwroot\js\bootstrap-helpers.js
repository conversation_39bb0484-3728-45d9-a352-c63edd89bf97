// Bootstrap Modal Helper Functions for Blazor Interop

window.bootstrapModal = {
    show: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }
    },

    hide: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }
    },

    toggle: function (modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
            modal.toggle();
        }
    }
};

// Keyboard Shortcuts Helper Functions for Blazor Interop
window.addKeyboardShortcuts = function (dotNetObjectReference) {
    console.log('Adding keyboard shortcuts...');

    // Remove existing event listener if it exists
    if (window.keyboardShortcutHandler) {
        document.removeEventListener('keydown', window.keyboardShortcutHandler);
    }

    // Create new event handler
    window.keyboardShortcutHandler = function (event) {
        // Prevent shortcuts when user is typing in input fields
        if (event.target.tagName === 'INPUT' ||
            event.target.tagName === 'TEXTAREA' ||
            event.target.isContentEditable) {
            return;
        }

        let shortcut = '';

        // Build shortcut string
        if (event.ctrlKey) shortcut += 'Ctrl+';
        if (event.altKey) shortcut += 'Alt+';
        if (event.shiftKey) shortcut += 'Shift+';

        // Handle special keys
        if (event.key === 'F5') {
            shortcut = 'F5';
            event.preventDefault(); // Prevent browser refresh
        } else if (event.key === 'r' && event.ctrlKey) {
            shortcut = 'Ctrl+R';
            event.preventDefault(); // Prevent browser refresh
        } else if (event.key === 'n' && event.ctrlKey) {
            shortcut = 'Ctrl+N';
            event.preventDefault(); // Prevent browser new window
        } else {
            return; // Not a shortcut we handle
        }

        // Call back to Blazor component
        try {
            dotNetObjectReference.invokeMethodAsync('HandleKeyboardShortcut', shortcut);
        } catch (error) {
            console.error('Error invoking keyboard shortcut handler:', error);
        }
    };

    // Add event listener
    document.addEventListener('keydown', window.keyboardShortcutHandler);

    console.log('Keyboard shortcuts added successfully');
};

// Remove keyboard shortcuts
window.removeKeyboardShortcuts = function () {
    if (window.keyboardShortcutHandler) {
        document.removeEventListener('keydown', window.keyboardShortcutHandler);
        window.keyboardShortcutHandler = null;
        console.log('Keyboard shortcuts removed');
    }
};
