using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Findings.Commands.CreateFindingFromAuditAnswer;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.SubmitAuditAnswer;

/// <summary>
/// Handler for SubmitAuditAnswerCommand
/// </summary>
public class SubmitAuditAnswerCommandHandler : BaseCommandHandler<SubmitAuditAnswerCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IMediator _mediator;

    public SubmitAuditAnswerCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IMediator mediator)
    {
        _context = context;
        _currentUserService = currentUserService;
        _mediator = mediator;
    }

    public override async Task<string> Handle(SubmitAuditAnswerCommand request, CancellationToken cancellationToken)
    {
        // Get the audit and question
        var audit = await _context.Audits
            .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

        if (audit == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Audits.Audit), request.AuditId);
        }

        var question = await _context.Questions
            .FirstOrDefaultAsync(q => q.Id == request.QuestionId, cancellationToken);

        if (question == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.Question), request.QuestionId);
        }

        // Check if answer already exists and update or create new
        var existingAnswer = await _context.AuditAnswers
            .Include(a => a.SelectedOptions)
            .Include(a => a.FailureReasons)
            .FirstOrDefaultAsync(a => a.AuditId == request.AuditId && a.QuestionId == request.QuestionId, 
                cancellationToken);

        AuditAnswer auditAnswer;

        if (existingAnswer != null)
        {
            // Update existing answer
            auditAnswer = existingAnswer;
            SetAnswerValue(auditAnswer, request.AnswerValue, question.QuestionType);
            auditAnswer.IsNotApplicable = request.IsNotApplicable;
            auditAnswer.Comments = request.Comments;
            auditAnswer.SeverityLevel = request.SeverityLevel;
            auditAnswer.UpdatedAt = DateTime.UtcNow;
            auditAnswer.UpdatedByUserId = _currentUserService.UserId;

            // Clear existing selected options and failure reasons
            _context.AuditAnswerSelectedOptions.RemoveRange(auditAnswer.SelectedOptions);
            _context.AuditAnswerFailureReasons.RemoveRange(auditAnswer.FailureReasons);
        }
        else
        {
            // Create new answer
            auditAnswer = new AuditAnswer
            {
                Id = CuidGenerator.Generate(),
                AuditId = request.AuditId,
                QuestionId = request.QuestionId,
                IsNotApplicable = request.IsNotApplicable,
                Comments = request.Comments,
                SeverityLevel = request.SeverityLevel,
                CreatedByUserId = _currentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            SetAnswerValue(auditAnswer, request.AnswerValue, question.QuestionType);
            await _context.AuditAnswers.AddAsync(auditAnswer, cancellationToken);
        }

        // Add selected options for multi-select questions
        foreach (var optionId in request.SelectedOptionIds)
        {
            var selectedOption = new AuditAnswerSelectedOption
            {
                Id = CuidGenerator.Generate(),
                AuditAnswerId = auditAnswer.Id,
                QuestionOptionId = optionId,
                CreatedAt = DateTime.UtcNow
            };
            await _context.AuditAnswerSelectedOptions.AddAsync(selectedOption, cancellationToken);
        }



        // Add failure reason texts if any (for PWA compatibility)
        var displayOrder = 0;
        foreach (var failureReasonText in request.FailureReasonTexts)
        {
            if (!string.IsNullOrWhiteSpace(failureReasonText))
            {
                var failureReason = new AuditAnswerFailureReason
                {
                    Id = CuidGenerator.Generate(),
                    AuditAnswerId = auditAnswer.Id,
                    ReasonText = failureReasonText.Trim(),
                    DisplayOrder = displayOrder++,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedByUserId = _currentUserService.UserId
                };
                await _context.AuditAnswerFailureReasons.AddAsync(failureReason, cancellationToken);
            }
        }

        // Update audit's last modified timestamp
        audit.UpdatedAt = DateTime.UtcNow;
        audit.UpdatedByUserId = _currentUserService.UserId;

        await _context.SaveChangesAsync(cancellationToken);

        // Attempt to create a finding if this answer indicates a failure
        try
        {
            var createFindingCommand = new CreateFindingFromAuditAnswerCommand
            {
                AuditAnswerId = auditAnswer.Id,
                ForceCreation = false
            };

            var findingResult = await _mediator.Send(createFindingCommand, cancellationToken);
            if (findingResult != null)
            {
                // Finding was created successfully
                // TODO: Add proper logging for successful finding creation
                System.Diagnostics.Debug.WriteLine($"Successfully created finding {findingResult.Id} for audit answer {auditAnswer.Id}");
            }
            else
            {
                // No finding was created (answer didn't indicate failure)
                System.Diagnostics.Debug.WriteLine($"No finding created for audit answer {auditAnswer.Id} - answer did not indicate failure");
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the audit answer submission
            System.Diagnostics.Debug.WriteLine($"Failed to create finding for audit answer {auditAnswer.Id}: {ex.Message}");
            // For now, continue without failing the audit answer submission
            // TODO: Add proper logging framework
        }

        return auditAnswer.Id;
    }

    private static void SetAnswerValue(AuditAnswer auditAnswer, string? answerValue, QuestionType questionType)
    {
        // Clear all answer fields first
        auditAnswer.AnswerBoolean = null;
        auditAnswer.AnswerText = null;
        auditAnswer.AnswerNumeric = null;
        auditAnswer.AnswerDate = null;
        auditAnswer.SelectedOptionId = null;
        auditAnswer.OriginalAnswerValue = null;

        if (string.IsNullOrEmpty(answerValue))
            return;

        // Store the original answer value for display purposes
        auditAnswer.OriginalAnswerValue = answerValue;

        // Set the appropriate field based on question type
        switch (questionType)
        {
            case QuestionType.YesNo:
                // Handle both boolean strings and Yes/No representations
                if (bool.TryParse(answerValue, out var boolValue))
                {
                    auditAnswer.AnswerBoolean = boolValue;
                }
                else
                {
                    // Handle common Yes/No representations
                    var normalizedValue = answerValue.Trim().ToLowerInvariant();
                    switch (normalizedValue)
                    {
                        case "yes":
                        case "y":
                            auditAnswer.AnswerBoolean = true;
                            break;
                        case "no":
                        case "n":
                            auditAnswer.AnswerBoolean = false;
                            break;
                        default:
                            // If it's not a recognized boolean value, store as text
                            auditAnswer.AnswerText = answerValue;
                            break;
                    }
                }
                break;
            case QuestionType.Numeric:
                if (decimal.TryParse(answerValue, out var numericValue))
                    auditAnswer.AnswerNumeric = numericValue;
                break;
            case QuestionType.Date:
                if (DateTime.TryParse(answerValue, out var dateValue))
                    auditAnswer.AnswerDate = dateValue;
                break;
            case QuestionType.SingleSelect:
                if (int.TryParse(answerValue, out var optionId))
                    auditAnswer.SelectedOptionId = optionId;
                break;
            case QuestionType.ShortText:
            case QuestionType.LongText:
            case QuestionType.MultiSelect:
            default:
                auditAnswer.AnswerText = answerValue;
                break;
        }
    }


}
